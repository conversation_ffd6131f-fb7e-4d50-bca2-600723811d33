<div [@routerTransition]>
    <!-- <div class="m-subheader">
        <div class="d-flex justify-content-between">
            <h3 class="m-subheader__title">{{langPoint("Management Approval Gift")}}</h3>
        </div>
    </div> -->
    <div class="m-content">
        <div class="m-portlet m-portlet--mobile">
            <div class="m-portlet__body">
                <div>
                    <div class="col-md-12 p-0">
                        <div class="row">
                            <div class="col-md-3">
                                <mat-form-field>
                                    <input matInput name="filterText" placeholder="{{l('GiftFilterPlaceHolder')}}"
                                        [(ngModel)]="filterText">
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <mat-select name="IsActiveFilter" [(ngModel)]="statusFilter" matInput
                                        placeholder="{{l('Status')}}">
                                        <mat-option *ngFor="let item of listStatus" [value]="item.value">{{ item.name
                                            }}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <mat-select name="changeStatus" [(ngModel)]="changeStatusFilter" matInput
                                        placeholder="{{l('Change Status')}}">
                                        <mat-option *ngFor="let item of listChangeStatus" [value]="item.value">{{ item.name
                                            }}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <mat-label>Vendor</mat-label>
                                    <mat-select [(ngModel)]="vendorIdFilter"
                                        (selectionChange)="onChangeVendor($event.value)" name="vendorSelect">
                                        <ngx-mat-select-search [placeholderLabel]="'Input for search'"
                                            [formControl]="searchCtrl"
                                            (input)="onSearchInputChange($event.target.value)">
                                        </ngx-mat-select-search>
                                        <mat-option *ngIf="isBusy" class="text-center">
                                            <i class="fa fa-spin fa-spinner"></i>
                                        </mat-option>
                                        <mat-option [value]="-1">{{l('All')}}</mat-option>
                                        <!-- <mat-option [value]='-1' *ngIf="isTeanantHost">{{l('LinkId')}}</mat-option> -->
                                        <!-- <mat-option [value]="tenantNameLogin" *ngIf="!isTeanantHost">{{tenantNameLogin}}</mat-option> -->
                                        <mat-option *ngFor="let item of list3rdPartyVendor" [value]="item.id">
                                            {{ item.vendorName }}
                                        </mat-option>
                                        <mat-option *ngIf="list3rdPartyVendor && list3rdPartyVendor.length === 0" [value]="'no-options'">No
                                            options
                                            found</mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <mat-select [disabled]="vendorFilter === -1 || !vendorFilter" name="ThirdPartyCategoryMappingByVendor" [(ngModel)]="thirdPartyCategoryMappingByVendorFilter" matInput
                                        placeholder="{{l('Third Party Category Mapping By Vendor')}}">
                                        <mat-option *ngFor="let item of list3rdPartyVendorMapping" [value]="item.thirdPartyCategoryId">{{ item.thirdPartyCategoryName
                                            }}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <mat-select [disabled]="vendorFilter === -1 || !vendorFilter" name="ThirdPartyBrandMappingByVendor" [(ngModel)]="thirdPartyBrandMappingByVendorFilter" matInput
                                        placeholder="{{l('Third Party Brand Mapping By Vendor')}}">
                                        <mat-option *ngFor="let item of listThirdPartyCategoryMappingByVendor" [value]="item.thirdPartyBrandId">{{ item.thirdPartyBrandName
                                            }}</mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <mat-label>Select BrandName</mat-label>
                                    <mat-select [formControl]="bankCtrl" #brandSelect
                                        (selectionChange)="brandSelectedSearch($event.value)">
                                        <mat-option>
                                            <ngx-mat-select-search [placeholderLabel]="'Input for search'"
                                                [formControl]="searchBrandCtrl"
                                                (input)="onSearchInputChangeBrand($event.target.value)">
                                            </ngx-mat-select-search>
                                        </mat-option>

                                        <mat-option *ngIf="isBusy" class="text-center">
                                            <i class="fa fa-spin fa-spinner"></i>
                                        </mat-option>
                                        <mat-option *ngFor="let item of listBrand" [value]="item">
                                            {{ item.name }}
                                        </mat-option>
                                        <mat-option *ngIf="listBrand && listBrand.length === 0"
                                            [value]="'no-options'">No options
                                            found</mat-option>
                                    </mat-select>
                                    <button mat-icon-button *ngIf="brandNameFilter" (click)="clearSelection()"
                                        class="clear-button">
                                        <mat-icon class="icon-clear">clear</mat-icon>
                                    </button>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <!-- <span style="color: red ; font-size : 20px">{{startTime}} </span>
                                    <span style="color: red ; font-size : 20px">{{programFLSDto.startTime}} </span> -->
                                    <input matInput [(ngModel)]="startTime" name="dateStartFrom"
                                        [matDatepicker]="dateStartFrom" placeholder="{{l('Effective From')}}"
                                        (dateInput)="changeExpireDate()">
                                    <mat-datepicker-toggle matSuffix [for]="dateStartFrom"></mat-datepicker-toggle>
                                    <mat-datepicker #dateStartFrom></mat-datepicker>

                                    <span class="txt-error" *ngIf="isafter">
                                        <mat-error>{{l('not valid !')}}</mat-error>
                                    </span>
                                </mat-form-field>


                            </div>
                            <div class="col-md-3">
                                <mat-form-field>
                                    <input matInput [(ngModel)]="endTime" name="dateStartTo"
                                        [matDatepicker]="dateStartTo" placeholder="{{l('Effective To')}}"
                                        (dateInput)="changeExpireDate()">
                                    <mat-datepicker-toggle matSuffix [for]="dateStartTo"></mat-datepicker-toggle>
                                    <mat-datepicker #dateStartTo></mat-datepicker>

                                    <span class="txt-error" *ngIf="isafter">
                                        <mat-error>{{l('not valid !')}}</mat-error>
                                    </span>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <ngx-dropdown-treeview-select [label]="langPoint('SelectParentCategoryGet')"
                                    [(value)]="parentLinkCategoryId" [items]="rootLinkIdHireachy"
                                    (valueChange)="onValueChange($event)">
                                </ngx-dropdown-treeview-select>
                            </div>

                            <div class="col-md-12 text-right mb-2">
                                <button (click)="refreshList()" *ngIf="dataSource.data.length > 0 && (selection.hasValue() || isAllSelected())"
                                    mat-raised-button class="mt-3 mr-2">
                                    {{l('Cancel')}}
                                </button>

                                <button (click)="handleOpenDialogReject()" [disabled]="isDisabled()"
                                    *ngIf="dataSource.data.length > 0 && (selection.hasValue() || isAllSelected())" mat-raised-button
                                    class="mt-3 btn-danger mr-2">
                                    {{l('Reject')}}
                                </button>

                                <button (click)="handleOpenDialogConfirm()" [disabled]="isDisabled()"
                                    *ngIf="dataSource.data.length > 0 && (selection.hasValue() || isAllSelected())" mat-raised-button
                                    class="mt-3 btn-info mr-2">
                                    {{l('Approval')}}
                                </button>

                                <button (click)="clearList()" mat-raised-button class="mr-2 mt-3" type="button">
                                    <i class="fa fa-refresh"></i> {{l('Clear')}}
                                </button>

                                <button (click)="refreshList()" [disabled]="isafter" mat-raised-button class="mr-2 mt-3 btn-info">
                                    <i class="fa fa-search "></i> {{l('Search')}}
                                </button>

                                <button type="button" (click)="exportTemplateNewGift()" mat-raised-button color="primary">
                                    {{langPoint("Export")}}
                                </button>
                            </div>
                            <div class="col-md-12 mb-2 d-flex">
                                <div class="col-md-4 text-center p-0">
                                    <div class="roi-budget-text h-100">
                                        <span>{{l('Total Request Pendding For Approve')}}: {{ totalRequestPenddingForApprove | number }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center p-0">
                                    <div class="roi-used-text percent h-100">
                                        <span>{{l('Total Request Approve')}}: {{ totalRequestApprove | number }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center p-0">
                                    <div class="roi-used-text percent-100 h-100">
                                        <span>{{l('Total Request Reject')}}: {{ totalRequestReject | number }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row d-flex justify-content-between no-gutters">
                    <div id="gifts" class="col-xl-12">
                        <table mat-table matSort [dataSource]="dataSource" (matSortChange)="sortChange($event)">
                            <ng-container matColumnDef="selectBox" sticky>
                                <th mat-header-cell *matHeaderCellDef class="text-center">
                                    <mat-checkbox (change)="masterToggle($event)" class="remove-checkbox-mg-label"
                                        color="primary" [checked]="selection.hasValue() && isAllSelected()"
                                        [indeterminate]="selection.hasValue() && !isAllSelected()">
                                    </mat-checkbox>
                                </th>
                                <td mat-cell *matCellDef="let row" style="padding-left: 24px;">
                                    <mat-checkbox *ngIf="row!= null" color="primary"
                                        (click)=" $event.stopPropagation()"
                                        (change)="rowSelected($event, row)" [checked]="selection.isSelected(row)">
                                    </mat-checkbox>
                                </td>
                            </ng-container>
                            
                            <ng-container matColumnDef="index">
                                <th mat-header-cell *matHeaderCellDef> {{l('NumberCount')}} </th>
                                <td mat-cell *matCellDef="let row; let i = index;">
                                    {{getIndex(i + 1)}}
                                </td>
                            </ng-container>
                            <!-- <ng-container matColumnDef="giftId">
                                <th mat-header-cell *matHeaderCellDef> {{l('GiftId')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    <a class="txt-href-link" (click)="editGift(row)">{{row.id}}</a>
                                </td>
                            </ng-container> -->
                            <ng-container matColumnDef="giftCode">
                                <th mat-header-cell *matHeaderCellDef> {{l('GiftCode')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    
                                    {{row.giftCode}}
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="requestGiftId">
                                <th mat-header-cell *matHeaderCellDef> {{l('requestGiftId')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    
                                    {{row.requestGiftId}}
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="giftName">
                                <th mat-header-cell *matHeaderCellDef> {{l('GiftName')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    
                                    {{row.giftName}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="vendorName">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{langPoint('Vendor')}} </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.vendorName ? row.vendorName : ''}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="approveUserName">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{langPoint('approve UserName')}}
                                </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.approveUserName ? row.approveUserName : ''}}
                                </td>
                            </ng-container>
                            
                            <ng-container class="text-center" matColumnDef="changeStatus">
                                <th class="number-format" mat-header-cell *matHeaderCellDef> {{l('change Status')}} </th>
                                <td class="number-format" mat-cell *matCellDef="let row">
                                    
                                    {{row.changeStatus}}
                                </td>
                            </ng-container>
                            <ng-container class="text-center" matColumnDef="itemUpdated">
                                <th class="number-format" mat-header-cell *matHeaderCellDef> {{l('item Updated')}} </th>
                                <td class="number-format" mat-cell *matCellDef="let row">
                                    
                                    {{row.itemUpdate}}
                                </td>
                            </ng-container>
                            <ng-container class="text-center" matColumnDef="oldValue">
                                <th class="number-format text-center" mat-header-cell *matHeaderCellDef> {{l('oldValue')}} </th>
                                <td class="number-format text-center" mat-cell *matCellDef="let row">
                                    <span class="text-bold">{{ cleanString(row.oldValue, row.itemUpdate) }}</span>
                                </td>
                            </ng-container>

                            <ng-container class="text-center" matColumnDef="newValue">
                                <th class="number-format text-center" mat-header-cell *matHeaderCellDef> {{l('newValue')}} </th>
                                <td class="number-format text-center" mat-cell *matCellDef="let row">
                                    <span class="text-bold">{{ cleanString(row.newValue, row.itemUpdate) }}</span>
                                </td>
                            </ng-container>

                            <ng-container class="text-center" matColumnDef="approveStatus">
                                <th class="number-format" mat-header-cell *matHeaderCellDef> {{l('approve Status')}} </th>
                                <td class="number-format" mat-cell *matCellDef="let row">
                                    
                                    {{row.approveStatus}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="createTime">
                                <th mat-header-cell *matHeaderCellDef class="text-center"> {{l("createTime")}} </th>
                                <td mat-cell *matCellDef="let row">
                                    <span *ngIf="row.createTime">
                                        {{row.createTime | date:'short'}}
                                    </span>
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="approveTime">
                                <th mat-header-cell *matHeaderCellDef class="text-center"> {{l("approveTime")}} </th>
                                <td mat-cell *matCellDef="let row">
                                    <span *ngIf="row.approveTime">
                                        {{row.approveTime | date:'short'}}
                                    </span>
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="giftCategoryName">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{langPoint('gift Category
                                    Name')}}
                                </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.giftCategoryName ? row.giftCategoryName : ''}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="thirdPartyCategoryName">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{langPoint('thirdParty
                                    Category Name')}}
                                </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.thirdPartyCategoryName ? row.thirdPartyCategoryName : ''}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="brandName">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{langPoint('LinkID Brand')}}
                                </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.brandName ? row.brandName : ''}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="thirdPartyBrandName">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{langPoint('thirdParty
                                    BrandName')}}
                                </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.thirdPartyBrandName ? row.thirdPartyBrandName : ''}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="action" stickyEnd>
                                <th mat-header-cell class="text-left" *matHeaderCellDef> Action </th>
                                <td mat-cell class="text-left" *matCellDef="let row">
                                    <button
                                        mat-icon-button (click)="$event.stopPropagation();viewDetail(row.requestGiftId, row.approveStatus)">
                                        <mat-icon>remove_red_eye</mat-icon>
                                    </button>
                                    
                                    <button mat-icon-button (click)="$event.stopPropagation();viewHistory(row.requestGiftId)"
                                        title="{{langPoint('View History')}}">
                                        <i class="la la-history" style="font-weight: bold;"></i>
                                    </button>
                                </td>
                            </ng-container>

                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                            <tr mat-row class="mat-row-hover" *matRowDef="let row; columns: displayedColumns;"></tr>
                        </table>
                    </div>
                </div>

                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" class="ml-auto" [showFirstLastButtons]="true"
                    (page)="pageChanged($event)" [length]="totalItems"></mat-paginator>
            </div>
        </div>
    </div>
</div>