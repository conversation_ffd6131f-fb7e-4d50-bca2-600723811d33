import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppCommonModule } from '@app/shared/common/app-common.module';
import { UtilsModule } from '@shared/utils/utils.module';
import { CountoModule } from 'angular2-counto';
import { ModalModule, TabsModule, TooltipModule, BsDropdownModule, PopoverModule } from 'ngx-bootstrap';
import { AutoCompleteModule, InputMaskModule } from 'primeng/primeng';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { NgxMaskModule } from 'ngx-mask';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { FileUploadModule as Ng2FileUploadModule } from 'ng2-file-upload';

// Additional Components
import { CampaignComboboxComponent } from '@app/campaign-combobox/campaign-combobox.component';
import { StatusDropdownListComponent } from '@app/commonControl/status-dropdown-list/status-dropdown-list.component';
import { TargetAudienceComboboxComponent } from '@app/target-audience-combobox/target-audience-combobox.component';
import { CreateEventAmountComponent } from '../eventSettings/create-event-amount/create-event-amount.component';
import { AutoCompleteSearchComponent } from '@app/commonControl/auto-complete-search/auto-complete-search.component';

// Settings Management Routing
import { SettingsManagementRoutingModule } from './settings-management-routing.module';

// Base Settings Components
import { BaseSettingsComponent } from '../baseSettings/baseSettings.component';
import { CreateOrEditBaseSettingModalComponent } from '../baseSettings/create-or-edit-baseSetting-modal.component';
import { ViewBaseSettingModalComponent } from '../baseSettings/view-baseSetting-modal.component';
import { SettingParamLookupTableModalComponent } from '../baseSettings/settingParam-lookup-table-modal.component';
import { CampaignLookupTableModalComponent } from '../baseSettings/campaign-lookup-table-modal.component';
import { SystemSettingsComponent } from '../systemSettings/systemSettings.component';
import { SpecialSettingsComponent } from '../specialSettings/specialSettings.component';
import { CreateOrEditSpecialSettingModalComponent } from '../specialSettings/create-or-edit-specialSetting-modal.component';
import { ViewSpecialSettingModalComponent } from '../specialSettings/view-specialSetting-modal.component';
import { SpecialSettingTypeComponent } from '../specialSettings/special-setting-type/special-setting-type.component';
import { SpecialsettingOcurTypeSelectComponent } from '../specialSettings/specialsetting-ocur-type-select/specialsetting-ocur-type-select.component';
import { ProductSettingsComponent } from '../productSettings/productSettings.component';
import { CreateOrEditProductSettingModalComponent } from '../productSettings/create-or-edit-productSetting-modal.component';
import { ViewProductSettingModalComponent } from '../productSettings/view-productSetting-modal.component';
import { FormulaSettingsComponent } from '../formulaSettings/formulaSettings.component';
import { ReasonSettingsComponent } from '../reasonSettings/reasonSettings.component';
import { ViewReasonSettingModalComponent } from '../reasonSettings/view-reasonSetting-modal.component';
import { EventSettingsComponent } from '../eventSettings/eventSettings.component';
import { CreateOrEditEventSettingModalComponent } from '../eventSettings/create-or-edit-eventSetting-modal.component';
import { ViewEventSettingModalComponent } from '../eventSettings/view-eventSetting-modal.component';
import { SettingParamsComponent } from '../settingParams/settingParams.component';
import { CreateOrEditSettingParamModalComponent } from '../settingParams/create-or-edit-settingParam-modal.component';
import { ViewSettingParamModalComponent } from '../settingParams/view-settingParam-modal.component';
import { CreateOrEditSettingParamsFieldComponent } from '../settingParams/create-or-edit-setting-params-field/create-or-edit-setting-params-field.component';
import { TreeValudFieldTypeSettingParramComponent } from '../settingParams/tree-valud-field-type-setting-parram/tree-valud-field-type-setting-parram.component';

// Rank Settings Components
import { RankSettingsComponent } from '../rankSettings/rankSettings.component';
import { RankRatioSettingsComponent } from '../rankRatioSettings/rankRatioSettings.component';
import { CreateOrEditRankRatioSettingModalComponent } from '../rankRatioSettings/create-or-edit-rankRatioSetting-modal.component';
import { ViewRankRatioSettingModalComponent } from '../rankRatioSettings/view-rankRatioSetting-modal.component';
import { RankTypesComponent } from '../rankTypes/rankTypes.component';
import { ViewRankTypeModalComponent } from '../rankTypes/view-rankType-modal.component';
import { RanksComponent } from '../ranks/ranks.component';
import { ViewRankModalComponent } from '../ranks/view-rank-modal.component';

// Member Activity Rule Settings Components
import { MemberActivityRuleSettingsComponent } from '../memberActivityRuleSettings/memberActivityRuleSettings.component';

// Period Setting Components
import { PeriodSettingComponent } from '../period-setting/period-setting.component';
import { ImportProductPeriodComponent } from '../period-setting/import-product-period/import-product-period.component';

// Event Template Components
import { EventTemplatesComponent } from '../eventTemplates/eventTemplates.component';
import { CreateOrEditEventTemplateComponent } from '../eventTemplates/create-or-edit-eventTemplates.component';

// Template Type Components
import { TemplatesTypeComponent } from '../templateType/templatesType.component';
import { CreateOrEditTemplateTypeComponent } from '../templateType/create-or-edit-templates-type.component';

// Events Components
import { EventsComponent } from '../events/events.component';

// Brand Components
import { BrandComponent } from '../brand/brand.component';
import { BrandServiceComponent } from '../brandService/brandService.component';

// Upload Image Components
import { UploadImageComponent } from '../uploadImage/uploadImage.component';

// Grant Type Components
import { GrantTypeComponent } from '../grant-type/grant-type.component';

// Location Management Components
import { LocationManagementComponent } from '../location-management/location-management.component';
import { LocationMappingComponent } from '../location-mapping/location-mapping.component';

// Partner System Components
import { PartnerSystemComponent } from '../partner-system/partner-system.component';

// Action Filter Components
import { ActionFilterComponent } from '../action-filter/action-filter.component';

// Actions Components
import { ActionsComponent } from '../actions/actions.component';

// Segment Components
import { SegmentComponent } from '../segment/segment.component';
import { CreateOrEditSegmentComponent } from '../segment/create-or-edit-segment.component';

// Kinds Components
import { KindsComponent } from '../kinds/kinds.component';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        AppCommonModule,
        UtilsModule,
        CountoModule,
        ModalModule.forRoot(),
        TabsModule.forRoot(),
        TooltipModule.forRoot(),
        BsDropdownModule.forRoot(),
        PopoverModule.forRoot(),
        AutoCompleteModule,
        InputMaskModule,
        TableModule,
        PaginatorModule,
        NgxMaskModule.forRoot(),
        MatCheckboxModule,
        MatExpansionModule,
        MatTabsModule,
        MatMenuModule,
        MatButtonModule,
        MatIconModule,
        MatFormFieldModule,
        MatSelectModule,
        MatInputModule,
        Ng2FileUploadModule,

        // Settings Management Routing
        SettingsManagementRoutingModule
    ],
    declarations: [
        // Base Settings Components
        BaseSettingsComponent,
        CreateOrEditBaseSettingModalComponent,
        ViewBaseSettingModalComponent,
        SettingParamLookupTableModalComponent,
        CampaignLookupTableModalComponent,
        SystemSettingsComponent,
        SpecialSettingsComponent,
        CreateOrEditSpecialSettingModalComponent,
        ViewSpecialSettingModalComponent,
        SpecialSettingTypeComponent,
        SpecialsettingOcurTypeSelectComponent,
        ProductSettingsComponent,
        CreateOrEditProductSettingModalComponent,
        ViewProductSettingModalComponent,
        FormulaSettingsComponent,
        ReasonSettingsComponent,
        ViewReasonSettingModalComponent,
        EventSettingsComponent,
        CreateOrEditEventSettingModalComponent,
        ViewEventSettingModalComponent,
        SettingParamsComponent,
        CreateOrEditSettingParamModalComponent,
        ViewSettingParamModalComponent,
        CreateOrEditSettingParamsFieldComponent,
        TreeValudFieldTypeSettingParramComponent,
        
        // Rank Settings Components
        RankSettingsComponent,
        RankRatioSettingsComponent,
        CreateOrEditRankRatioSettingModalComponent,
        ViewRankRatioSettingModalComponent,
        RankTypesComponent,
        ViewRankTypeModalComponent,
        RanksComponent,
        ViewRankModalComponent,
        
        // Member Activity Rule Settings Components
        MemberActivityRuleSettingsComponent,
        
        // Period Setting Components
        PeriodSettingComponent,
        ImportProductPeriodComponent,
        
        // Event Template Components
        EventTemplatesComponent,
        CreateOrEditEventTemplateComponent,
        
        // Template Type Components
        TemplatesTypeComponent,
        CreateOrEditTemplateTypeComponent,
        
        // Events Components
        EventsComponent,
        
        // Brand Components
        BrandComponent,
        BrandServiceComponent,
        
        // Upload Image Components
        UploadImageComponent,
        
        // Grant Type Components
        GrantTypeComponent,
        
        // Location Management Components
        LocationManagementComponent,
        LocationMappingComponent,
        
        // Partner System Components
        PartnerSystemComponent,
        
        // Action Filter Components
        ActionFilterComponent,
        
        // Actions Components
        ActionsComponent,
        
        // Segment Components
        SegmentComponent,
        CreateOrEditSegmentComponent,
        
        // Kinds Components
        KindsComponent,

        // Additional Components
        CampaignComboboxComponent,
        StatusDropdownListComponent,
        TargetAudienceComboboxComponent,
        CreateEventAmountComponent
    ]
})
export class SettingsManagementModule { }
