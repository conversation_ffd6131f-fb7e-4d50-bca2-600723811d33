<!-- <mat-form-field floatLabel="never" class="search-box" *ngIf="!hasSelect">
    <input [(ngModel)]="searchTextFilter" type="text"
        (keyup)="handleSearchBrandFilter($event)" (input)="searchChangedBrandFilter()" matInput placeholder="{{placeholderText}}"
        maxlength="255" #brandFilter [formControl]="targetSearchFilter" [matAutocomplete]="autoBrandFilter" [readonly]="isDisabled || !vendorIdFilter" />
    <mat-icon (click)="onButtonSearchClickBrandFilter()" matSuffix *ngIf="!isDisabled">search</mat-icon>
    <mat-icon matSuffix *ngIf="isDisabled">search</mat-icon>
    <mat-autocomplete id="filtered-products" autoActiveFirstOption disableRipple="true" #autoBrandFilter="matAutocomplete"
        (optionSelected)="targetSelected($event.option.value)">
        <mat-option class="product-filter-item" *ngFor="let t of filteredBrandFilter | async" [value]="t">
            <div class="product-search-results-item">
                <div>
                    <b>{{ t.thirdPartyBrandName }}</b>
                </div>
            </div>
        </mat-option>
    </mat-autocomplete>
</mat-form-field> -->
<mat-form-field *ngIf="!hasSelect && !multiSelected">
    <mat-label>{{placeholderText}}</mat-label>
    <mat-select [formControl]="brandServerSideCtrl"
        (selectionChange)="changeValueSelect($event)" [disabled]="isDisabled" [required]="isRequired">
      <mat-option>
        <ngx-mat-select-search [formControl]="brandServerSideFilteringCtrl"
            [searching]="searching" [placeholderLabel]="'Input for search'"
            [noEntriesFoundLabel]="'No data'"></ngx-mat-select-search>
      </mat-option>
      <mat-option *ngIf="!multiSelected && hasDefaultNameAll">{{defaultNameAll}}</mat-option>
      <mat-option *ngFor="let brand of filteredServerSidebrands | async" [value]="brand">
        {{brand.thirdPartyBrandName}}
      </mat-option>
    </mat-select>
</mat-form-field>
<mat-form-field *ngIf="!hasSelect && multiSelected">
    <mat-label>{{placeholderText}}</mat-label>
    <mat-select [formControl]="brandServerSideCtrl" [multiple]="true" [required]="isRequired"
        (selectionChange)="changeValueSelect($event)" [disabled]="isDisabled">
      <mat-option>
        <ngx-mat-select-search [formControl]="brandServerSideFilteringCtrl"
            [searching]="searching" [placeholderLabel]="'Input for search'"
            [noEntriesFoundLabel]="'No data'"></ngx-mat-select-search>
      </mat-option>
      <mat-option *ngFor="let brand of tempData" [value]="brand">
        {{brand.thirdPartyBrandName}}
      </mat-option>
      <mat-option *ngFor="let brand of getDataListBrand()" [value]="brand">
        {{brand.thirdPartyBrandName}}
      </mat-option>
    </mat-select>
</mat-form-field>
<mat-form-field *ngIf="hasSelect">
    <input type="text" [value]="selectedValue.thirdPartyBrandName" matInput name="brandMaping{{selectedValue.id}}"
        disabled placeholder="{{placeholderText}}">
    <mat-icon matSuffix (click)="removebrandIdChange()">close</mat-icon>
</mat-form-field>