<div class="main-page m-3">
	<div [busyIf]="primengTableHelper.isLoading">
		<form #segmentForm="ngForm" (keydown)="segmentForm.form.markAsDirty()">
			<div class="container-advance-target">
				<div class="audience-advance-target">
					<div class="d-flex justify-content-between px-4 pt-3">
							<div class="col-md-11 mb-1 header-pd-top" style="display: -webkit-inline-box;">
								<h4 *ngIf="id" class="text-primary">{{langPoint("Pages_Segment_Edit")}}</h4>
								<h4 *ngIf="!id" class="text-primary">{{langPoint("Pages_Segment_Create")}}</h4>
							</div>
							<div class="align-button-right">
								<button mat-icon-button class="text-muted">
									<mat-icon (click)="cancel()" aria-label="Close">close</mat-icon>
								</button>
							</div>
					</div>
					
					<div class="d-flex px-4 pt-3">
						<div class="row col-md-6 mb-1">
							<input name="name" type="text" required class="form-control custom"
								placeholder="{{langPoint('Name') }}" [(ngModel)]="listCondition.name" />
						</div>
						<div class="col-md-4 mb-1">
							<select required class="form-control custom" name="segmentType"
								[(ngModel)]="listCondition.type" (change)="changeType($event)" *ngIf="!isEdit">
								<option value="ProductPurchasingBehavior">
									{{langPoint("Pages_Segment_Productpurchasingbehavior")}}</option>
								<option value="OrderPurchasingBehavior">
									{{langPoint("Pages_Segment_Orderpurchasingbehavior")}}</option>
								<option value="MemberList">{{langPoint("Pages_Segment_MemberList")}}</option>
							</select>
							<select required class="form-control custom" name="segmentType"
								[(ngModel)]="listCondition.type" (change)="changeType($event)" *ngIf="isEdit">
								<option *ngIf="listCondition.type != 'MemberList'" value="ProductPurchasingBehavior">
									{{langPoint("Pages_Segment_Productpurchasingbehavior")}}</option>
								<option *ngIf="listCondition.type != 'MemberList'" value="OrderPurchasingBehavior">
									{{langPoint("Pages_Segment_Orderpurchasingbehavior")}}</option>
								<option *ngIf="listCondition.type == 'MemberList'" value="MemberList">
									{{langPoint("Pages_Segment_MemberList")}}
								</option>
							</select>
						</div>
						<a [routerLink]="" *ngIf="this.listCondition.type === 'ProductPurchasingBehavior'" class="pl-3 pt-1" rel="help" (click)="displayDescription()">{{langPoint("Creation_Guide")}}</a>	
					</div>
				</div>
				<div *ngIf="isShowValidProductCode != 2">
					<div class="p-4 main-page-content">
						<div class="content-advance-target">
							<ng-template ngFor let-item [ngForOf]="listCondition.segmentDetailsModel" let-i="index"
								[ngForTrackBy]="trackByFn">
								<div class="item-target">
									<p class="p-title">{{langPoint("Pages_Segment_Condition")}} {{ i + 1 }}<label class="required-fields">(*)</label></p>
									<div class="row" *ngFor="let child of item.items; let idx = index">
										<div class="col-lg-2 col-md-2 mb-1 no-pd-right">
											<input name="lowerValue_{{ i }}_{{ idx }}" type="text" currencyInput required
												class="form-control custom" [(ngModel)]="child.lowerValue" />
										</div>
										<div class="mb-1 margin-operator">
											<select required name="lowerCondition__{{ i }}_{{ idx }}"
												class="form-control custom width-operator" [(ngModel)]="child.lowerCondition">
												<option value="&lt;">&lt;</option>
												<option value="&lt;=">&lt;=</option>
												<option value="=">=</option>
											</select>
										</div>
										<div class="col-lg-2 col-md-2 mb-1 no-pd-right no-pd-left">
											<select required class="form-control custom" name="param_{{ i }}_{{ idx }}"
												[(ngModel)]="child.param">
												<option *ngFor="let item of dataLov" value="{{ item.loV.value }}">
													{{ item.loV.value }}
												</option>
											</select>
										</div>
										<div class="mb-1 margin-operator">
											<select required class="form-control custom width-operator"
												name="upperCondition_{{ i }}_{{ idx }}" [(ngModel)]="child.upperCondition">
												<option value="&lt;">&lt;</option>
												<option value="&lt;=">&lt;=</option>
												<option value="=">=</option>
											</select>
										</div>
										<div class="col-lg-2 col-md-2  no-pd-left">
											<input name="upperValue_{{ i }}_{{ idx }}" type="text" required
												class="form-control custom" [(ngModel)]="child.upperValue" currencyInput />
										</div>
										<p class="text-append" *ngIf="isShowValidProductCode == 1">
											{{langPoint("Pages_Segment_And")}}</p>
										<div class="col-md-2" *ngIf="isShowValidProductCode == 1">
											<mat-form-field floatLabel="never" class="search-box">
												<input #inputSearchProduct [formControl]="productSearch" type="text"
													id="inputSearchProduct_{{ i }}_{{ idx }}"
													(keyup)="handleProductSearch($event)" (input)="searchProductChanged()"
													matInput placeholder="{{langPoint('InputProductCodeForSearching')}}" maxlength="255"
													[matAutocomplete]="autoSegment" name="productList_{{ i }}_{{ idx }}" />
												<mat-autocomplete id="filtered-products" autoActiveFirstOption
													disableRipple="true" #autoSegment="matAutocomplete" (optionSelected)="
												targetProductSelected($event.option.value, i, idx)">
													<mat-option class="product-filter-item"
														*ngFor="let t of filteredProduct | async" [value]="t">
														<div class="product-search-results-item">
															<div>
																<b>{{ t.product.code }} - {{ t.product.name }}</b>
															</div>
														</div>
														<mat-divider></mat-divider>
													</mat-option>
												</mat-autocomplete>
											</mat-form-field>
										</div>
										<p class="text-append"
											*ngIf="item.items.length > 1 && idx != item.items.length - 1">
											Or
										</p>
										<button mat-icon-button class="remove-item" type="button"
											*ngIf="item.items.length > 1"
											(click)="$event.stopPropagation(); deleteLevel(i, idx)">
											<mat-icon>delete_outline</mat-icon>
										</button>
										<div class="listProductSearch" *ngIf="isShowValidProductCode == 1">
											<mat-chip-list>
												<mat-chip *ngFor="let product of child.productListModel"
													ariaOrientation="horizontal" removable="true"
													(removed)="removeTarget(product, i, idx)">
													{{ product.code }} - {{ product.name }}
													<mat-icon matChipRemove>cancel</mat-icon>
												</mat-chip>
											</mat-chip-list>
										</div>
									</div>
									<div class="row button-add-level">
										<button class="btn btn-info" (click)="addLevel(i)">
											{{langPoint("Pages_Segment_Add_Level")}}
										</button>
									</div>
								</div>
								<div class="operator-where">
									<div class="row">
										<div class="col-md-3"
											*ngIf=" listCondition.segmentDetailsModel.length > 1 && i != listCondition.segmentDetailsModel.length - 1 ">
											<select required class="form-control custom" name="combination_{{ i }}"
												[(ngModel)]="item.combination">
												<option value="and">{{langPoint("Pages_Segment_Combination_And")}}</option>
												<option value="or">{{langPoint("Pages_Segment_Combination_Or")}}</option>
											</select>
										</div>
										<div class="col-md-3">
											<button mat-icon-button class="remove-item" type="button"
												*ngIf="listCondition.segmentDetailsModel.length > 1"
												(click)="$event.stopPropagation(); deleteCondition(i)">
												<mat-icon>delete_outline</mat-icon>
											</button>
										</div>
									</div>
								</div>
							</ng-template>
						</div>
						<div class=" align-button-left" *ngIf="isShowValidProductCode != 2">
							<button class="btn btn-info" (click)="addCondition()">
								{{langPoint("Pages_Segment_Add_Condition")}}
							</button>
						</div>
					</div>
				</div>
				<div class="px-4 py-2 main-page-content" *ngIf="isShowValidProductCode == 2">
					<div class="row">
						<div class="col-md-6">
							<div class="custom-file disable-btn">
								<input type="file" class="custom-file-input" #inputImport id="importFile" ng2FileSelect
									[uploader]="uploader" (change)="onFileChange($event)" accept=".xlsx" />
								<label class="custom-file-label" for="importFile">{{labelImport}}</label>
							</div>
							<div class="col-form-label">
								<a href="{{ templateUrl }}">{{langPoint("DownloadTemplate") }}</a>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="align-control-button-right">
						<button class="btn btn-info" (click)="save()"
							[disabled]="checkDisableSave(segmentForm)">{{langPoint("Save")}}</button>
						<button class="btn btn-info" (click)="cancel()">{{langPoint("Cancel")}}</button>
						<button class="btn btn-info" [disabled]="checkDisableGenerateMemberList(segmentForm)" (click)="generateMemberList(id)"
							*ngIf="isEdit == true && listCondition.type != 'MemberList'">{{langPoint("Pages_Segment_Generate")}}</button>
					</div>
				</div>
			</div>
		</form>
		<div class="col-xl-12" *ngIf="isEdit == true">
			<div>
				<div class="table-responsive segment-product-period-list" id="segment-product-period-list">
					<table mat-table [dataSource]="memberList">
						<ng-container matColumnDef="id">
							<th mat-header-cell *matHeaderCellDef>#</th>
							<td mat-cell *matCellDef="let index = index">
								{{index + 1}}
							</td>
						</ng-container>
						<ng-container matColumnDef="memberCode">
							<th mat-header-cell *matHeaderCellDef>
								{{ langPoint("MemberCode") }}
							</th>
							<td mat-cell *matCellDef="let row; let index = index">
								{{ row.memberCode }}
							</td>
						</ng-container>
						<ng-container matColumnDef="action">
							<th mat-header-cell *matHeaderCellDef>
								{{langPoint("Action") }}
							</th>
							<td mat-cell *matCellDef="let row">
								<button class="btn btn-link"
									(click)="remove(row.memberCode, id)">{{langPoint('Remove')}}</button>
							</td>
						</ng-container>

						<tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
						<tr mat-row class="mat-row-hover" *matRowDef="let row; columns: displayColumns"></tr>
					</table>
				</div>
				<mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" class="width-100" #paginator [pageSize]="pageSize" [showFirstLastButtons]="true"
					(page)="pageChanged($event)" [length]="totalItems"></mat-paginator>
			</div>
		</div>
</div>
</div>