<div [@routerTransition]>
    <!-- <div class="m-subheader">
        <div class="d-flex justify-content-between">
            <h3 class="m-subheader__title">{{langPoint("Management Approval Gift")}}</h3>
        </div>
    </div> -->
    <div class="m-content">
        <div class="m-portlet m-portlet--mobile">
            <div class="m-portlet__body">
                <div>
                    <div class="col-md-12 p-0">
                        <div class="row">
                            <div class="col-md-3">
                                <mat-form-field>
                                    <input matInput name="thirdPartyTransactionId" placeholder="{{l('Mã hóa đơn')}}"
                                        [(ngModel)]="thirdPartyTransactionId">
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <input matInput name="giftName" placeholder="{{l('Tên mệnh giá')}}"
                                        [(ngModel)]="giftName">
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <input matInput name="transactionCode" placeholder="{{l('Transaction Code')}}"
                                        [(ngModel)]="transactionCode">
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <mat-label>Select BrandName</mat-label>
                                    <mat-select [formControl]="bankCtrl" #brandSelect
                                        (selectionChange)="brandSelectedSearch($event.value)">
                                        <mat-option>
                                            <ngx-mat-select-search [placeholderLabel]="'Input for search'"
                                                [formControl]="searchBrandCtrl"
                                                (input)="onSearchInputChangeBrand($event.target.value)">
                                            </ngx-mat-select-search>
                                        </mat-option>

                                        <mat-option *ngIf="isBusy" class="text-center">
                                            <i class="fa fa-spin fa-spinner"></i>
                                        </mat-option>
                                        <mat-option *ngFor="let item of listBrand" [value]="item">
                                            {{ item.name }}
                                        </mat-option>
                                        <mat-option *ngIf="listBrand && listBrand.length === 0"
                                            [value]="'no-options'">No options
                                            found</mat-option>
                                    </mat-select>
                                    <button mat-icon-button *ngIf="brandNameFilter" (click)="clearSelection()"
                                        class="clear-button">
                                        <mat-icon class="icon-clear">clear</mat-icon>
                                    </button>
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <input matInput name="eGiftCode" placeholder="{{l('Mã Voucher')}}"
                                        [(ngModel)]="eGiftCode">
                                </mat-form-field>
                            </div>

                            <div class="col-md-3">
                                <mat-form-field>
                                    <input matInput name="thirdPartyBrandId" placeholder="{{l('Mã cửa hàng')}}"
                                        [(ngModel)]="thirdPartyBrandId">
                                </mat-form-field>
                            </div>

                            <div class="col-md-12 text-right mb-2">
                                <button (click)="clearList()" mat-raised-button class="mr-2 mt-3" type="button">
                                    <i class="fa fa-refresh"></i> {{l('Clear')}}
                                </button>

                                <button (click)="refreshList()" [disabled]="isafter" mat-raised-button class="mr-2 mt-3 btn-info">
                                    <i class="fa fa-search "></i> {{l('Search')}}
                                </button>

                                <button type="button" (click)="export()" mat-raised-button color="primary">
                                    {{langPoint("Export")}}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row d-flex justify-content-between no-gutters">
                    <div id="gifts" class="col-xl-12">
                        <table mat-table matSort [dataSource]="dataSource" (matSortChange)="sortChange($event)">
                            
                            <ng-container matColumnDef="index">
                                <th mat-header-cell *matHeaderCellDef> {{l('NumberCount')}} </th>
                                <td mat-cell *matCellDef="let row; let i = index;">
                                    {{getIndex(i + 1)}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="transactionCode">
                                <th mat-header-cell *matHeaderCellDef> {{l('transactionCode')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    
                                    {{row.transactionCode}}
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="brandName">
                                <th mat-header-cell *matHeaderCellDef> {{l('brandName')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    
                                    {{row.brandName}}
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="thirdPartyTransactionId">
                                <th mat-header-cell *matHeaderCellDef> {{l('Mã hóa đơn')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    
                                    {{row.thirdPartyTransactionId}}
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="eGiftCode">
                                <th mat-header-cell *matHeaderCellDef> {{l('Mã Voucher')}} </th>
                                <td mat-cell *matCellDef="let row">
                                    
                                    {{row.eGiftCode}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="giftName">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{l('giftName')}} </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.giftName}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="coin">
                                <th mat-header-cell class="text-center" *matHeaderCellDef> {{l('coin')}}
                                </th>
                                <td mat-cell class="text-right" *matCellDef="let row">
                                    {{row.coin}}
                                </td>
                            </ng-container>
                            
                            <ng-container class="text-center" matColumnDef="thirdPartyBrandId">
                                <th class="number-format" mat-header-cell *matHeaderCellDef> {{l('Mã cửa hàng')}} </th>
                                <td class="number-format" mat-cell *matCellDef="let row">
                                    
                                    {{row.thirdPartyBrandId}}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="createTime">
                                <th mat-header-cell *matHeaderCellDef class="text-center"> {{l("createTime")}} </th>
                                <td mat-cell *matCellDef="let row">
                                    <span *ngIf="row.createTime">
                                        {{row.createTime | date:'short'}}
                                    </span>
                                </td>
                            </ng-container>

                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                            <tr mat-row class="mat-row-hover" *matRowDef="let row; columns: displayedColumns;"></tr>
                        </table>
                    </div>
                </div>

                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" class="ml-auto" [showFirstLastButtons]="true"
                    (page)="pageChanged($event)" [length]="totalItems"></mat-paginator>
            </div>
        </div>
    </div>
</div>