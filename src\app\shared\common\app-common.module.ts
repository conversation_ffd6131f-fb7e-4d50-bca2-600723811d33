import { AbpModule } from '@abp/abp.module';
import * as ngCommon from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppLocalizationService } from '@app/shared/common/localization/app-localization.service';
import { AppNavigationService } from '@app/shared/layout/nav/app-navigation.service';
import { CommonModule } from '@shared/common/common.module';
import { UtilsModule } from '@shared/utils/utils.module';
import { ModalModule } from 'ngx-bootstrap';
import { PaginatorModule } from 'primeng/primeng';
import { TableModule } from 'primeng/table';
import { AppAuthService } from './auth/app-auth.service';
import { AppRouteGuard } from './auth/auth-route-guard';
import { JqPluginDirective } from './libs/jq-plugin.directive';
import { CommonLookupModalComponent } from './lookup/common-lookup-modal.component';
import { DatePickerDirective } from './timing/date-picker.component';
import { DateRangePickerComponent } from './timing/date-range-picker.component';
import { DateTimeService } from './timing/date-time.service';
import { TimeZoneComboComponent } from './timing/timezone-combo.component';
import { Moment } from 'moment';
import * as moment from 'moment';
import { UserDateCustomPipe } from './timing/user-date-format';
import { DateCustomPipe } from './timing/date-format';
import { StyleWidthDirective } from '../directives/StyleWidth.directive';
import { DateTimeInputComponent } from '../../commonControl/datetime-input/datetime-input.component';
import { AdvanceTargetCampaignComponent } from '../../commonControl/advance-target-campaign/advance-target-campaign';
import { TargetTableMemberListComponent } from '../../commonControl/advance-target-campaign/table-member-list/table-member-list.component';
import { TargetAudienceCampaignComponent } from '../../commonControl/target-audience-campaign/target-audience-campaign';
import { TargetAudienceSearch } from '../../main/akaPoint/shared/target-audience-search';

// Angular Material
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatNativeDateModule } from '@angular/material/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatChipsModule } from '@angular/material/chips';
import { MatCheckboxModule } from '@angular/material/checkbox';

// NGX Timepicker
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';

// NGX Mat Select Search
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';

// Shared Components
import { InputTelNumberComponent } from '@app/shared/input-tel-number/input-tel-number.component';
import { AutoCompleteSearchComponent } from '@app/commonControl/auto-complete-search/auto-complete-search.component';
import { ImportLocationMappingModalComponent } from '@app/main/akaPoint/location-mapping/import-location-mapping-modal.component';
import { CreateOrEditRegionModalComponent } from '@app/main/akaPoint/regions/create-or-edit-region-modal.component';
import { ViewRegionModalComponent } from '@app/main/akaPoint/regions/view-region-modal.component';

@NgModule({
    imports: [
        ngCommon.CommonModule,
        FormsModule,
        ReactiveFormsModule,
        ModalModule.forRoot(),
        UtilsModule,
        AbpModule,
        CommonModule,
        TableModule,
        PaginatorModule,

        // Angular Material modules
        MatDatepickerModule,
        MatInputModule,
        MatNativeDateModule,
        MatAutocompleteModule,
        MatDividerModule,
        MatFormFieldModule,
        MatSelectModule,
        MatIconModule,
        MatTableModule,
        MatPaginatorModule,
        MatChipsModule,
        MatCheckboxModule,

        // NGX Timepicker
        NgxMaterialTimepickerModule.forRoot(),

        // NGX Mat Select Search
        NgxMatSelectSearchModule
    ],
    declarations: [
        TimeZoneComboComponent,
        JqPluginDirective,
        CommonLookupModalComponent,
        DateRangePickerComponent,
        DatePickerDirective,
        UserDateCustomPipe,
        DateCustomPipe,
        StyleWidthDirective,
        DateTimeInputComponent,
        AdvanceTargetCampaignComponent,
        TargetTableMemberListComponent,
        TargetAudienceCampaignComponent,
        TargetAudienceSearch,
        InputTelNumberComponent,
        AutoCompleteSearchComponent,
        ImportLocationMappingModalComponent,
        CreateOrEditRegionModalComponent,
        ViewRegionModalComponent
    ],
    exports: [
        TimeZoneComboComponent,
        JqPluginDirective,
        CommonLookupModalComponent,
        DateRangePickerComponent,
        DatePickerDirective,
        UserDateCustomPipe,
        DateCustomPipe,
        StyleWidthDirective,
        DateTimeInputComponent,
        AdvanceTargetCampaignComponent,
        TargetTableMemberListComponent,
        TargetAudienceCampaignComponent,
        TargetAudienceSearch,
        InputTelNumberComponent,
        AutoCompleteSearchComponent,
        ImportLocationMappingModalComponent,
        CreateOrEditRegionModalComponent,
        ViewRegionModalComponent,

        // Angular modules
        ReactiveFormsModule,

        // Angular Material modules
        MatDatepickerModule,
        MatInputModule,
        MatNativeDateModule,
        MatAutocompleteModule,
        MatDividerModule,
        MatFormFieldModule,
        MatSelectModule,
        MatIconModule,
        MatTableModule,
        MatPaginatorModule,
        MatChipsModule,
        MatCheckboxModule,

        // NGX Timepicker
        NgxMaterialTimepickerModule,

        // NGX Mat Select Search
        NgxMatSelectSearchModule
    ],
    providers: [
        DateTimeService,
        AppLocalizationService,
        AppNavigationService
    ]
})
export class AppCommonModule {
    static forRoot(): ModuleWithProviders {
        return {
            ngModule: AppCommonModule,
            providers: [
                AppAuthService,
                AppRouteGuard
            ]
        };
    }

    static isDisableDate(item: Moment) {
        if (!item) {
            return false;
        }
        if (item.isSameOrAfter(moment(), 'date')) {
            return false;
        }
        else {
            return true;
        }

    }
}
