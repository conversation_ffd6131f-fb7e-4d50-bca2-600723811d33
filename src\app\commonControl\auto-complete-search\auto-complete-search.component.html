<ng-container *ngIf="!nameSelectedDefault">
  <mat-form-field *ngIf="!hasSelect && !multiSelected">
    <mat-label>{{placeholderText}}</mat-label>
    <mat-select #singleSelect [formControl]="autoCompleteCtrl" [required]="isRequired"
      (selectionChange)="changeValueSelect($event)" [disabled]="isDisabled">
      <mat-option>
        <ngx-mat-select-search [formControl]="autoCompleteFilterCtrl" [placeholderLabel]="'Input for search'"
          [noEntriesFoundLabel]="'No data'"></ngx-mat-select-search>
      </mat-option>
      <mat-option *ngIf="!multiSelected && hasDefaultNameAll">{{defaultNameAll}}</mat-option>
      <mat-option *ngFor="let bank of filteredDataAutoComplete | async" [value]="bank">
        {{bank[dataNameValue]}}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field *ngIf="!hasSelect && multiSelected">
    <mat-label>{{placeholderText}}</mat-label>
    <mat-select #singleSelect [formControl]="autoCompleteCtrl" [multiple]="true" [required]="isRequired"
      (selectionChange)="changeValueSelect($event)" [disabled]="isDisabled">
      <mat-option>
        <ngx-mat-select-search [formControl]="autoCompleteFilterCtrl" [placeholderLabel]="'Input for search'"
          [noEntriesFoundLabel]="'No data'"></ngx-mat-select-search>
      </mat-option>
      <mat-option *ngFor="let bank of filteredDataAutoComplete | async" [value]="bank">
        {{bank[dataNameValue]}}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field *ngIf="hasSelect">
    <input type="text" [value]="selectedValueName" matInput disabled placeholder="{{placeholderText}}">
    <mat-icon matSuffix (click)="removebrandIdChange()">close</mat-icon>
  </mat-form-field>
</ng-container>

<ng-container *ngIf="nameSelectedDefault">
  <mat-form-field>
    <input type="text" [value]="nameSelectedDefault" matInput disabled placeholder="{{placeholderText}}">
    <mat-icon matSuffix (click)="removebrandIdChange()">close</mat-icon>
  </mat-form-field>
</ng-container>