<div [@routerTransition]>
  <div class="m-subheader">
    <div class="d-flex align-items-center">
      <div class="mr-auto col-xs-6">
        <h3 class="m-subheader__title">
          <span>{{langPoint("Brand Service Management")}}</span>
        </h3>
      </div>
      <div class="col-md-6 ml-auto">
        <button mat-flat-button color="primary" (click)="createBrand()" class="pull-right">
          <mat-icon>add</mat-icon> {{langPoint("Create New BrandService")}}
        </button>
      </div>
    </div>
  </div>
  <div class="m-content">
    <div class="m-portlet m-portlet--mobile">
      <div class="m-portlet__body">
        <div class="row pb-0">
          <div class="col-md-3">
            <mat-form-field>
              <input matInput [(ngModel)]="nameFilter" placeholder="{{l('BrandService Name')}}" name="nameFilter">
            </mat-form-field>

          </div>
          <div class="col-md-3">
            <mat-form-field>
              <input matInput [(ngModel)]="codeFilter" placeholder="{{l('BrandService Code')}}" name="codeFilter">
            </mat-form-field>
          </div>

          <div class="col-md-3">
            <!-- <app-auto-complete-search [dataNameValue]="'name'"
                    [dataListValues]="listBrand" [dataIdValue]="'id'" [placeholderText]="langPoint('BrandName')"
                    (autoSelectedValue)="brandSelectedSearch($event)" #internalBrandSearch
                    [dataNameValue]="'name'"></app-auto-complete-search> -->
            <!-- <mat-form-field> -->
            <!-- <input matInput name="brandFilter" placeholder="{{langPoint('BrandName')}}"
                    [(ngModel)]="brandFilter"> -->
            <!-- </mat-form-field> -->
            <mat-form-field>
              <mat-label>Select BrandName</mat-label>
              <mat-select [formControl]="bankCtrl" #brandSelect (selectionChange)="brandSelectedSearch($event.value)">
                <mat-option>
                  <ngx-mat-select-search [placeholderLabel]="'Input for search'"
                  [formControl]="searchCtrl"
                  (input)="onSearchInputChange($event.target.value)">
                  </ngx-mat-select-search>
                </mat-option>
                
                <mat-option *ngIf="isBusy" class="text-center">
                  <i class="fa fa-spin fa-spinner"></i>
                </mat-option>
                <mat-option *ngFor="let item of listBrandDto" [value]="item">
                  {{ item.name }}
                </mat-option>
                <mat-option *ngIf="listBrandDto && listBrandDto.length === 0" [value]="'no-options'">No options
                  found</mat-option>
              </mat-select>
              <button mat-icon-button *ngIf="brandCodeFilter" (click)="clearSelection()" class="clear-button">
                <mat-icon class="icon-clear">clear</mat-icon>
              </button>
            </mat-form-field>
          </div>
          <!-- <div class="col-md-2">
            <mat-form-field class="w-100">
              <mat-select name="statusFilter" [(value)]="statusFilter" matInput placeholder="{{l('Status')}}">
                <mat-option *ngFor="let item of listStatus" [value]="item.value">{{ item.viewValue }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div> -->
          <div class="col-md-7 button-alway-right">
            <button class="btn btn-info pull-right" name="searchAdvance" type="button" (click)="search()">
              <i class="fa fa-search "></i> {{langPoint('Search')}}
            </button>
            <button style="margin-right:5px" class="btn pull-right" name="clear" type="button"
              (click)="clearBrandList()">
              <i class="fa fa-refresh"></i> {{langPoint('Clear')}}
            </button>
          </div>
        </div>
        <div class="row margin-bottom-10">
          <div class="col-sm-12">
          </div>
        </div>
        <div class="row">
          <div class="col-xl-12 table-responsive">
            <table mat-table matSort [dataSource]="dataSource" (matSortChange)="sortChange($event)">
              <ng-container matColumnDef="index">
                <th mat-header-cell *matHeaderCellDef> {{l('NumberCount')}} </th>
                <td mat-cell *matCellDef="let row; let i = index;">
                  {{getIndex(i + 1)}}
                </td>
              </ng-container>
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
                <td mat-cell *matCellDef="let row">{{row.id}}</td>
              </ng-container>
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{l('Code')}} </th>
                <td mat-cell *matCellDef="let row">{{row.code}}</td>
              </ng-container>
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{l('Name')}} </th>
                <td mat-cell *matCellDef="let row">{{row.brandServicesName}}</td>
              </ng-container>
              <!-- <ng-container matColumnDef="brandName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{l('BrandName')}} </th>
                <td mat-cell *matCellDef="let row">{{row.name}}</td>
              </ng-container> -->
              <ng-container matColumnDef="brandName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{l('Brand Name')}} </th>
                <td mat-cell *matCellDef="let row">{{row.brandName}}</td>
              </ng-container>
              <ng-container matColumnDef="creationTime">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{l('creationTime')}} </th>
                <td mat-cell *matCellDef="let row">{{row.creationTime | userdate:'short'}}</td>
              </ng-container>
              <ng-container matColumnDef="lastModificationTime">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{l('LastUpdatedTime')}} </th>
                <td mat-cell *matCellDef="let row">{{row.lastModificationTime | userdate:'short'}}</td>
              </ng-container>
              <ng-container matColumnDef="lastModifierUserName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{l('LastUpdateBy')}} </th>
                <td mat-cell *matCellDef="let row">{{row.lastModifierUserName}}</td>
              </ng-container>
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef [ngClass]="'customWidthClass'"> {{l('Actions')}} </th>
                <td mat-cell *matCellDef="let row">
                  <button mat-icon-button (click)="editBrand(row)">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button
                    (click)="delete(row)">
                    <mat-icon>delete_outline</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row class="mat-row-hover" *matRowDef="let row; columns: displayedColumns;"></tr>

            </table>

            <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" #paginator [pageSize]="pageSize"
              [showFirstLastButtons]="true" (page)="pageChanged($event)" [length]="totalItems"></mat-paginator>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>