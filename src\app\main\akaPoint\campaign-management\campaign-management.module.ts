import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppCommonModule } from '@app/shared/common/app-common.module';
import { UtilsModule } from '@shared/utils/utils.module';
import { CountoModule } from 'angular2-counto';
import { ModalModule, TabsModule, TooltipModule, BsDropdownModule, PopoverModule } from 'ngx-bootstrap';
import { AutoCompleteModule, InputMaskModule } from 'primeng/primeng';
import { NgxMaskModule } from 'ngx-mask';

// Additional modules for missing components
import { NgxEditorModule } from 'ngx-editor';
import { FileUploadModule as Ng2FileUploadModule } from 'ng2-file-upload';

// Shared modules
import { MaterialModule } from '@shared/common/common.module';
import { AkaPointSharedModule } from '../shared/akapoint-shared.module';

// Campaign Management Routing
import { CampaignManagementRoutingModule } from './campaign-management-routing.module';

// Campaign Components
import { CampaignsComponent } from '../campaigns/campaigns.component';
import { CreateOrEditCampaignModalComponent } from '../campaigns/create-or-edit-campaign-modal.component';
import { ImportFileForCampaignComponent } from '../campaigns/import-file-for-campaign/import-file-for-campaigns.component';

// Purchase Campaign Components
import { CreateOrEditPurchaseCampaignModalComponent } from '../campaigns/create-or-edit/purchase-campaign/create-or-edit-purchase-campaign-modal.component';

// Action Campaign Components
import { CreateOrEditActionCampaignModalComponent } from '../campaigns/create-or-edit/action-rule-campaign/create-or-edit-action-rule-campaign-modal.component';

// First Order Campaign Components
import { CreateOrEditFirstOrderRuleCampaignModalComponent } from '../campaigns/create-or-edit/first-order-rule/create-or-edit-first-order-rule-campaign-modal.component';

// Non-Purchase Campaign Components
import { CreateOrEditNonPurchaseCampaignModalComponent } from '../campaigns/create-or-edit/non-purchase-campaign/create-or-edit-non-purchase-campaign-modal.component';

// Referral Campaign Components
import { CreateOrEditReferralCampaignModalComponent } from '../campaigns/create-or-edit/referral-campaign/create-or-edit-referral-campaign-modal.component';

// Seniority Campaign Components
import { CreateOrEditSeniorityCampaignModalComponent } from '../campaigns/create-or-edit/seniority-campaign/create-or-edit-seniority-campaign-modal.component';

// MLM Campaign Components
import { CreateOrEditMLMPurchaseOrderCampaignModalComponent } from '../campaigns/create-or-edit/mlm-purchase-order-campaign/create-or-edit-mlm-purchase-order-campaign-modal.component';

// Memory Land Campaign Components
import { CreateOrEditMemoryLandCampaignModalComponent } from '../campaigns/create-or-edit/memory-land-campaign/create-or-edit-memory-land-campaign-modal.component';

// Campaign Report Components
import { EarningCampaignReport } from '../earningCampaignReport/earningCampaignReport.component';
import { MemoryLandReportComponent } from '../memory-land-report/memory-land-report.component';

// Region Modal Components (needed by EarningCampaignReport)
import { CreateOrEditRegionModalComponent } from '../regions/create-or-edit-region-modal.component';
import { ViewRegionModalComponent } from '../regions/view-region-modal.component';

// Additional Components (needed by MemoryLandReportComponent and CampaignInfoComponent)
import { ActionValueFilterSearchComponent } from '@app/commonControl/action-filter-value-search/action-filter-value-search.component';

import { DateInputComponent } from '@app/commonControl/date-input/date-input.component';
import { CronJobsComponent } from '@app/commonControl/cron-jobs/cron-jobs.component/cron-jobs.component';
import { MatMonthPickerComponent, MonthPickerHeader } from '@app/commonControl/monthpicker.component/monthpicker.component';

// Custom Tab Components (needed by PurchaseCampaignComponent)
import { CustomTabGroupComponent } from '@app/commonControl/custom-tab/custom-tab-group.component';
import { CustomTabComponent } from '@app/commonControl/custom-tab/custom-tab';
import { CustomTabHeaderComponent } from '@app/commonControl/custom-tab/custom-tab-header';
import { CustomTabContentComponent } from '@app/commonControl/custom-tab/custom-tab-content';

// Purchase Campaign Sub-Components
import { PurchaseBaseRatioComponent } from '../campaigns/purchase-campaign/purchase-campaign-baseratio/purchase-campaign-baseratio.component';
import { PurchaseReturnBaseRatioComponent } from '../campaigns/purchase-campaign/return-base-ratio/return-base-ratio.component';
import { PurchaseRankRatioComponent } from '../campaigns/purchase-campaign/rank-ratio/rank-ratio.component';
import { PurchaseEventRatioComponent } from '../campaigns/purchase-campaign/event-ratio/event-ratio.component';
import { ProductCategoryRatioComponent } from '../campaigns/purchase-campaign/product-category-ratio/product-category-ratio';
import { ProductPointSettingComponent } from '../campaigns/purchase-campaign/product-point/product-point';

// Non-Purchase Campaign Sub-Components
import { SpecialSettingComponent } from '../campaigns/non-purchase-campaign/special-setting-item/special-setting-item.component';

// Action Rule Campaign Sub-Components
import { ActionRuleCampaignInfoComponent } from '../campaigns/action-rule-campaign/action-rule-campaign-info/action-rule-campaign-info.component';

// Seniority Campaign Sub-Components
import { SeniorityItemComponent } from '../campaigns/seniority-campaign/seniority-item/seniority-item.component';

// Memory Land Campaign Sub-Components
import { MemoryLandCampaignInfoComponent } from '../campaigns/memory-land-campaign/memory-land-campaign/memory-land-campaign-info.component';

// MLM Campaign Sub-Components
import { MlmPurchaseReturnSettingComponent } from '../campaigns/mlm-purchase-order-campaign/mlm-purchase-return-setting/mlm-purchase-return-setting.component';
import { MLMActionRuleCampaignComponent } from '../campaigns/mlm-action-rule-campaign/mlm-action-rule-campaign.component';
import { MLMActionRuleCampaignInfoComponent } from '../campaigns/mlm-action-rule-campaign/mlm-action-rule-campaign-info/mlm-action-rule-campaign-info.component';

// Campaign Sub-Components (used in templates)
import { CampaignInfoComponent } from '../campaigns/campaign-info/campaign-info.component';
import { PurchaseCampaignComponent } from '../campaigns/purchase-campaign/purchase-campaign.component';
import { NonPurchaseCampaignComponent } from '../campaigns/non-purchase-campaign/non-purchase-campaign.component';
import { ActionRuleCampaignComponent } from '../campaigns/action-rule-campaign/action-rule-campaign.component';
import { SeniorityCampaignComponent } from '../campaigns/seniority-campaign/seniority.component';
import { ReferralCampaignComponent } from '../campaigns/referral-campaign/referral.component';
import { FirstOrderRuleComponent } from '../campaigns/first-order-rule/first-order-rule.component';
import { MemoryLandCampaignComponent } from '../campaigns/memory-land-campaign/memory-land-campaign.component';
import { ReferralCampaignDetailComponent } from '../campaigns/referral-campaign-detail/referral-campaign-detail.component';
import { MLMPurchaseOrderCampaignComponent } from '../campaigns/mlm-purchase-order-campaign/mlm-purchase-order-campaign.component';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        AppCommonModule,
        UtilsModule,
        CountoModule,
        ModalModule.forRoot(),
        TabsModule.forRoot(),
        TooltipModule.forRoot(),
        BsDropdownModule.forRoot(),
        PopoverModule.forRoot(),
        AutoCompleteModule,
        InputMaskModule,
        NgxMaskModule.forRoot(),

        // Additional modules for missing components
        NgxEditorModule,
        Ng2FileUploadModule,

        // AkaPoint Shared Module (provides directives like RatioInputDirective)
        AkaPointSharedModule,

        // Material modules
        MaterialModule,

        // Campaign Management Routing
        CampaignManagementRoutingModule
    ],
    declarations: [
        // Campaign Components
        CampaignsComponent,
        CreateOrEditCampaignModalComponent,
        ImportFileForCampaignComponent,
        
        // Purchase Campaign Components
        CreateOrEditPurchaseCampaignModalComponent,
        
        // Action Campaign Components
        CreateOrEditActionCampaignModalComponent,
        
        // First Order Campaign Components
        CreateOrEditFirstOrderRuleCampaignModalComponent,
        
        // Non-Purchase Campaign Components
        CreateOrEditNonPurchaseCampaignModalComponent,
        
        // Referral Campaign Components
        CreateOrEditReferralCampaignModalComponent,
        
        // Seniority Campaign Components
        CreateOrEditSeniorityCampaignModalComponent,
        
        // MLM Campaign Components
        CreateOrEditMLMPurchaseOrderCampaignModalComponent,
        
        // Memory Land Campaign Components
        CreateOrEditMemoryLandCampaignModalComponent,
        
        // Campaign Report Components
        EarningCampaignReport,
        MemoryLandReportComponent,

        // Region Modal Components (needed by EarningCampaignReport)
        CreateOrEditRegionModalComponent,
        ViewRegionModalComponent,

        // Additional Components (needed by MemoryLandReportComponent and CampaignInfoComponent)
        ActionValueFilterSearchComponent,
        DateInputComponent,
        CronJobsComponent,
        MatMonthPickerComponent,
        MonthPickerHeader,

        // Custom Tab Components (needed by PurchaseCampaignComponent)
        CustomTabGroupComponent,
        CustomTabComponent,
        CustomTabHeaderComponent,
        CustomTabContentComponent,

        // Purchase Campaign Sub-Components
        PurchaseBaseRatioComponent,
        PurchaseReturnBaseRatioComponent,
        PurchaseRankRatioComponent,
        PurchaseEventRatioComponent,
        ProductCategoryRatioComponent,
        ProductPointSettingComponent,

        // Non-Purchase Campaign Sub-Components
        SpecialSettingComponent,

        // Action Rule Campaign Sub-Components
        ActionRuleCampaignInfoComponent,

        // Seniority Campaign Sub-Components
        SeniorityItemComponent,

        // Memory Land Campaign Sub-Components
        MemoryLandCampaignInfoComponent,

        // MLM Campaign Sub-Components
        MlmPurchaseReturnSettingComponent,
        MLMActionRuleCampaignComponent,
        MLMActionRuleCampaignInfoComponent,

        // Campaign Sub-Components (used in templates)
        CampaignInfoComponent,
        PurchaseCampaignComponent,
        NonPurchaseCampaignComponent,
        ActionRuleCampaignComponent,
        SeniorityCampaignComponent,
        ReferralCampaignComponent,
        FirstOrderRuleComponent,
        MemoryLandCampaignComponent,
        ReferralCampaignDetailComponent,
        MLMPurchaseOrderCampaignComponent
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CampaignManagementModule { }
