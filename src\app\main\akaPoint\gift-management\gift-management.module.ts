import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppCommonModule } from '@app/shared/common/app-common.module';
import { UtilsModule } from '@shared/utils/utils.module';
import { CountoModule } from 'angular2-counto';
import { ModalModule, TabsModule, TooltipModule, BsDropdownModule, PopoverModule } from 'ngx-bootstrap';
import { AutoCompleteModule, InputMaskModule } from 'primeng/primeng';
import { NgxMaskModule } from 'ngx-mask';

// Shared modules
import { MaterialModule } from '@shared/common/common.module';

// Third-party modules
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { TreeviewModule } from 'ngx-treeview';
import { FileUploadModule as Ng2FileUploadModule } from 'ng2-file-upload';

// Custom components
import { DropdownTreeviewSelectComponent } from '@app/commonControl/dropdown-treeview-select/dropdown-treeview-select.component';

import { GiftCategorySearchComponent } from '@app/commonControl/gift-category-search/gift-category-search';
import { GiftGroupSearchComponent } from '../gift/gift-group-search/gift-group-search';
import { GiftSearchRegionComponent } from '../gift/gift-search-region/gift-search-region.component';

// Cash Voucher components
import { CashComponent } from '../gift/cash-voucher/cash/cash.component';
import { BillComponent } from '../gift/cash-voucher/bill/bill.component';
import { EvoucherPaidComponent } from '../gift/cash-voucher/evoucher-paid/evoucher-paid.component';

// Gift Management Routing
import { GiftManagementRoutingModule } from './gift-management-routing.module';

// Gift Components
import { GiftListComponent } from '../gift/gift-list.component';
import { CreateOrEditGiftComponent } from '../gift/create-or-edit/create-or-edit-gift.component';
import { CreateOrEditGiftTopupComponent } from '../gift/evoucher-topup/create-or-edit/create-or-edit-gift-topup.component';
import { EVoucherTopUpComponent } from '../gift/evoucher-topup/evoucher-topup.component';
import { EVoucherLookUpComponent } from '../gift/evoucher-topup/lookup-code/evoucher-lookup.component';
import { GiftExportFileEgiftComponent } from '../gift/gift-export-file-egift/gift-export-file-egift.component';
import { ListGiftExportFileEgiftComponent } from '../gift/gift-export-file-egift/list-gift-export-file-egift/list-gift-export-file-egift.component';
import { GiftInfomationComponent } from '../gift/gift-infomation/gift-infomation.component';
import { EGiftComponent } from '../gift/e-gift/e-gift.component';
import { GiftTargetAudienceComponent } from '../gift/gift-target-audience/gift-target-audience.component';
import { GiftDistributeEgiftComponent } from '../gift/gift-distribute-egift/gift-distribute-egift.component';
import { GiftDistributeEgiftOfflineComponent } from '../gift/gift-distribute-egift-offline/gift-distribute-egift-offline.component';
import { GiftUpdateEgiftStatusComponent } from '../gift/gift-update-egift-status/gift-update-egift-status.component';
import { SendCustomerComponent } from '../gift/send-gift-to-customers/send-gift-to-customer.component';
import { SearchGiftKeyWordComponent } from '../gift/search-gift-key-word/search-gift-key-word.component';
import { FeedBackSuggestionComponent } from '../gift/feedback-suggestion/feedback-suggestion.component';
import { CreateOrEditFeedBackModalComponent } from '../gift/feedback-suggestion/create-or-edit-feedback-modal.component';
import { InternalGiftModificationComponent } from '../gift/internal-gift-modification/internal-gift-modification.component';
import { TopUpMobileComponent } from '../gift/topUp-mobile/topUp-mobile.component';
import { ExportRequestComponent } from '../gift/export-request/export-request.component';
import { BlackGiftListComponent } from '../gift/blackgift-gift/blackgift-gift.component';
import { ApprovalGiftComponent } from '../gift/approval-gift/approval-gift.component';
import { CashVoucherComponent } from '../gift/cash-voucher/cash-voucher.component';
import { GiftAddressComponent } from '../gift/gift-address/gift-address.component';
import { CreateOrEditGiftAddressModalComponent } from '../gift/gift-address/create-or-edit-gift-address-modal.component';

// Gift Group Components
import { GiftGroupComponent } from '../gift-group/gift-group.component';
import { CreateOrEditGiftGroupComponent } from '../gift-group/create-or-edit-gift-group/create-or-edit-gift-group.component';
import { AddGiftToGiftGroupComponent } from '../gift-group/add-gift-to-gift-group/add-gift-to-gift-group.component';
import { EditGiftToGiftGroupComponent } from '../gift-group/edit-gift-to-gift-group/edit-gift-to-gift-group.component';
import { TokenTransDetailTemplateComponent } from '../gift-group/token-transaction-detail/token-trans-detail-template.component';
import { CreateOrEditTokenTransDetailTemplateComponent } from '../gift-group/token-transaction-detail/create-or-edit-token-trans-detail/create-or-edit-token-trans-detail-template.component';

// Gift Category Components
import { GifCategoryComponent } from '../gif-category/gif-category.component';
import { GifCategoryTableListComponent } from '../gif-category/table-category/gift-category-table-list.component';

// Gift Transaction Components
import { GiftRedeemTransactionComponent } from '../gift-redeem-transaction/gift-redeem-transaction.component';
import { GiftRedeemTransactionThirdPartyComponent } from '../giftredeemtransactionthird-party/giftredeemtransactionthird-party.component';
import { GiftReconciliationVendorComponent } from '../gift-reconciliation-vendor/gift-reconciliation-vendor.component';
import { DetailThirdPartyGiftRedeemComponent } from '../gift-reconciliation-vendor/detail-thirdparty-gift-redeem/detail-thirdparty-gift-redeem.component';

// Gift Settings Components
import { GiftMonitorSettingComponent } from '../gift-monitor-setting/gift-monitor-setting.component';
import { MerchantGiftOrderSettingComponent } from '../merchant-gift-order-setting/merchant-gift-order-setting.component';
import { MerchantAllowGiftSettingComponent } from '../merchant-allow-gift-setting/merchant-allow-gift-setting.component';

// Give Gift Components
import { GiveGiftComponent } from '../give-gift/give-gift.component';
import { CreateOrEditGiveGiftComponent } from '../give-gift/create-or-edit-give-gift/create-or-edit-give-gift.component';

// Flash Sale Components
import { ProgramFlashSaleComponent } from '../flash-sale/program-flashsale.component';
import { CreateOrEditProductFlashSaleComponent } from '../flash-sale/create-or-edit-product-sale/create-or-edit-product-sale.component';

// Third Party Gift Components
import { ThirdpartyGiftComponent } from '../thirdparty-gift/thirdparty-gift.component';
import { ThirdPartyGiftVendorComponent } from '../thirdparty-gift-vendor/thirdparty-gift-vendor.component';
import { ThirdPartyMappingDataComponent } from '../thirdparty-gift-vendor/thirdparty-mapping-data/thirdparty-mapping-data.component';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        AppCommonModule,
        UtilsModule,
        CountoModule,
        ModalModule.forRoot(),
        TabsModule.forRoot(),
        TooltipModule.forRoot(),
        BsDropdownModule.forRoot(),
        PopoverModule.forRoot(),
        AutoCompleteModule,
        InputMaskModule,
        NgxMaskModule.forRoot(),

        // Material modules
        MaterialModule,

        // Third-party modules
        NgxMatSelectSearchModule,
        TreeviewModule,
        Ng2FileUploadModule,

        // Gift Management Routing
        GiftManagementRoutingModule
    ],
    declarations: [
        // Gift Components
        GiftListComponent,
        CreateOrEditGiftComponent,
        CreateOrEditGiftTopupComponent,
        EVoucherTopUpComponent,
        EVoucherLookUpComponent,
        GiftExportFileEgiftComponent,
        ListGiftExportFileEgiftComponent,
        GiftInfomationComponent,
        EGiftComponent,
        GiftTargetAudienceComponent,
        GiftDistributeEgiftComponent,
        GiftDistributeEgiftOfflineComponent,
        GiftUpdateEgiftStatusComponent,
        SendCustomerComponent,
        SearchGiftKeyWordComponent,
        FeedBackSuggestionComponent,
        CreateOrEditFeedBackModalComponent,
        InternalGiftModificationComponent,
        TopUpMobileComponent,
        ExportRequestComponent,
        BlackGiftListComponent,
        ApprovalGiftComponent,
        CashVoucherComponent,
        GiftAddressComponent,
        CreateOrEditGiftAddressModalComponent,
        
        // Gift Group Components
        GiftGroupComponent,
        CreateOrEditGiftGroupComponent,
        AddGiftToGiftGroupComponent,
        EditGiftToGiftGroupComponent,
        TokenTransDetailTemplateComponent,
        CreateOrEditTokenTransDetailTemplateComponent,
        
        // Gift Category Components
        GifCategoryComponent,
        GifCategoryTableListComponent,
        
        // Gift Transaction Components
        GiftRedeemTransactionComponent,
        GiftRedeemTransactionThirdPartyComponent,
        GiftReconciliationVendorComponent,
        DetailThirdPartyGiftRedeemComponent,
        
        // Gift Settings Components
        GiftMonitorSettingComponent,
        MerchantGiftOrderSettingComponent,
        MerchantAllowGiftSettingComponent,
        
        // Give Gift Components
        GiveGiftComponent,
        CreateOrEditGiveGiftComponent,
        
        // Flash Sale Components
        ProgramFlashSaleComponent,
        CreateOrEditProductFlashSaleComponent,
        
        // Third Party Gift Components
        ThirdpartyGiftComponent,
        ThirdPartyGiftVendorComponent,
        ThirdPartyMappingDataComponent,

        // Custom Components
        DropdownTreeviewSelectComponent,
        GiftCategorySearchComponent,
        GiftGroupSearchComponent,
        GiftSearchRegionComponent,

        // Cash Voucher Components
        CashComponent,
        BillComponent,
        EvoucherPaidComponent
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class GiftManagementModule { }
